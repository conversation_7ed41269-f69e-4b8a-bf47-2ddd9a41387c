#!/usr/bin/env python3
"""
Test script to verify tab filtering functionality in the Chat Center.
This script will test the backend API directly to see if tab filtering is working.
"""

import requests
import json
import sys
from urllib.parse import urlencode

# Configuration
BACKEND_URL = "http://localhost:8001"  # Updated to match PUBLIC_BACKEND_URL
API_ENDPOINT = f"{BACKEND_URL}/customer/api/platform-identities/"

def test_tab_filtering():
    """Test tab filtering with different parameters"""
    
    # Test parameters
    test_cases = [
        {
            "name": "No filtering (should return all)",
            "params": {"page": 1, "page_size": 5}
        },
        {
            "name": "My Assigned tab (admin username)",
            "params": {"page": 1, "page_size": 5, "tab": "my-assigned", "current_user": "admin"}
        },
        {
            "name": "My Closed tab (admin username)",
            "params": {"page": 1, "page_size": 5, "tab": "my-closed", "current_user": "admin"}
        },
        {
            "name": "Open tab",
            "params": {"page": 1, "page_size": 5, "tab": "open", "current_user": "admin"}
        },
        {
            "name": "Others Assigned tab (admin username)",
            "params": {"page": 1, "page_size": 5, "tab": "others-assigned", "current_user": "admin"}
        },
        {
            "name": "My Assigned tab (test username)",
            "params": {"page": 1, "page_size": 5, "tab": "my-assigned", "current_user": "test"}
        }
    ]
    
    print("Testing Chat Center Tab Filtering")
    print("=" * 50)
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        print(f"Parameters: {test_case['params']}")
        
        try:
            # Make API request
            url = f"{API_ENDPOINT}?{urlencode(test_case['params'])}"
            print(f"URL: {url}")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"✅ Status: {response.status_code}")
                print(f"📊 Results count: {len(results)}")
                print(f"🔢 Total count: {data.get('count', 'N/A')}")
                
                # Show first few results with ticket info
                if results:
                    print("📋 Sample results:")
                    for i, result in enumerate(results[:3]):
                        ticket_status = result.get('latest_ticket_status', 'N/A')
                        ticket_owner = result.get('latest_ticket_owner', 'N/A')
                        platform_user = result.get('platform_username', 'N/A')
                        
                        print(f"  {i+1}. {platform_user} | Status: {ticket_status} | Owner: {ticket_owner}")
                else:
                    print("📋 No results returned")
                    
            else:
                print(f"❌ Status: {response.status_code}")
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            
        print("-" * 30)

if __name__ == "__main__":
    test_tab_filtering()
